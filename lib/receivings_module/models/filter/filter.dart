import 'package:app/receivings_module/models/filter/filter_value.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'filter.freezed.dart';

typedef FilterValueInputDelegate = Future<String?> Function(
  Map<String, FilterValueModel>,
)?;

@freezed
class FilterModel with _$FilterModel {
  const factory FilterModel.query({
    required String id,
    required String label,
    @Default(false) bool eanScannerEnabled,
    @Default(false) bool qrScannerEnabled,
  }) = QueryFilterModel;

  const factory FilterModel.list({
    required String id,
    required String label,
    required List<FilterValueModel<String>> values,
  }) = ListFilterModel;

  const factory FilterModel.dateRange({
    required String id,
    required String label,
  }) = DateRangeFilterModel;

  const factory FilterModel.text({
    required String id,
    required String label,
    required String hintText,
    required int maxLength,
    FilterValueInputDelegate valueInputDelegate,
  }) = TextFilterModel;

  const factory FilterModel.numeric({
    required String id,
    required String label,
    required String hintText,
  }) = NumericFilterModel;
}
