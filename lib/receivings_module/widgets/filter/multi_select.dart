import 'package:app/receivings_module/models/filter/filter.dart';
import 'package:app/receivings_module/models/filter/filter_value.dart';
import 'package:app/receivings_module/widgets/filter/heading.dart';
import 'package:app/receivings_module/widgets/filter/list_item.dart';
import 'package:app/shared/cubits/filter/cubit.dart';
import 'package:app/shared/cubits/filter/state.dart';
import 'package:app/shared/helpers/i18n.dart';
import 'package:app/shared/widgets/gap.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class MultiSelectFilter<T extends FilterCubit> extends StatelessWidget {
  final MultiSelectFilterModel filter;

  const MultiSelectFilter({
    Key? key,
    required this.filter,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final tr = getTranslator(context);

    return BlocBuilder<T, FilterState>(
      buildWhen: (previousState, currentState) {
        return currentState.values[filter.id] !=
                previousState.values[filter.id] ||
            currentState.isExpanded(filter.id) !=
                previousState.isExpanded(filter.id);
      },
      builder: (_, state) {
        // display checkboxes with multiselect functionality
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            FilterHeading(
              label: tr(filter.label),
            ),
            ...filter.values.map(
              (fv) => Padding(
                padding: const EdgeInsets.all(4),
                child: ListFilterItem(
                  filterValue: fv,
                  isSelected: state.values[filter.id]!.value.contains(fv.value),
                  onSelected: () {
                    context.read<T>().add(
                          id: filter.id,
                          value: FilterValueModel<List<NovaModel>>(
                            value: [
                              ...state.values[filter.id]!.value,
                              fv.value,
                            ],
                          ),
                        );
                  },
                  onDeselected: () {
                    context.read<T>().add(
                          id: filter.id,
                          value: FilterValueModel<List<NovaModel>>(
                            value: state.values[filter.id]!.value
                                .where((v) => v != fv.value)
                                .toList(),
                          ),
                        );
                  },
                ),
              ),
            ),
            const Gap(4),
            const Divider(),
          ],
        );
      },
    );
  }
}
